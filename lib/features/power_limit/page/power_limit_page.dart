import 'package:flutter/services.dart';
import 'package:flutter_basic/components/custom_card.dart';
import 'package:flutter_basic/components/custom_image_asset.dart';
import 'package:flutter_basic/components/picker/picker_single.dart';
import 'package:flutter_basic/features/monitor/monitor.dart';
import 'package:flutter_basic/features/power_limit/model/view_model.dart';
import 'package:flutter_basic/features/system_detail/config/SystemConfig.dart';
import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/platform/utils/color_utils.dart';
import 'package:flutter_basic/repositories/monitor_repository/model/monitor_response_graph_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../components/bottom_button.dart';
import '../../../components/custom_switch.dart';
import '../../../components/picker/picker_time_slot_range.dart';
import '../../../generated/l10n.dart';
import '../../../platform/community/monitor/model_builders.dart';
import '../../../platform/utils/version.dart';
import '../../monitor/bloc/mqtt/mqtt_bloc.dart';
import '../bloc/bloc.dart';

class PowerLimitPage extends StatefulWidget {
  const PowerLimitPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return PowerLimitPageState();
  }
}

class PowerLimitPageState extends State<PowerLimitPage> {
  List<PowerLimitVM> _recordList = [];
  bool initStateFlag = false;
  bool innerInvoke = false;
  String identifier = '';
  String modelKey = '';
  String productKey = '';
  String deviceNo = '';
  bool _smartSocketPriorityEnabled = true;
  @override
  void initState() {
    super.initState();
    _recordList = [];
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final monitorBloc = BlocProvider.of<MonitorFetchBloc>(context);
    if (initStateFlag) {
      return;
    }
    initStateFlag = true;
    if (LocalModeManger.instance.isLocalModeNow) {
      _getDataFormLocalMode();
      return;
    }
    dynamic arguments = ModalRoute.of(context)?.settings.arguments;
    if (arguments != null) {
      String? data = arguments["data"] ?? '';
      if (arguments['from'] != null) {
        var json = monitorBloc
            .state.monitorModel?.energyFlowChartVO?.otherLoadVo?.otherLoadJson;
        if (json != null) {
          _recordList = PowerLimitVM.fromJsonArray(json);
        }
      } else {
        if (data != null && data.isNotEmpty) {
          _recordList = PowerLimitVM.fromJsonArray(data);
        }
      }

      identifier = arguments['identifier'] ?? '';
      modelKey = arguments['modelKey'] ?? '';
      productKey = arguments['productKey'] ?? '';
      deviceNo = arguments['deviceNo'] ?? '';
      innerInvoke = arguments['innerInvoke'] ?? false;
      logger.d("other data $data");
    }

    // 初始化智能插座优先开关状态
    _initSmartSocketPriorityState();
  }

  _getDataFormLocalMode() async {
    try {
      dynamic arguments = ModalRoute.of(context)?.settings.arguments;
      deviceNo = arguments['deviceNo'] ?? '';
      innerInvoke = true;
      var res = await LocalModeManger.instance.getOtherLoadPower();
      var model = buildModel(res);
      var json = model.energyFlowChartVO?.otherLoadVo?.otherLoadJson;
      if (json != null) {
        _recordList = PowerLimitVM.fromJsonArray(json);
      }
      logger.d("other data from local model $_recordList");
      // 在本地模式下也初始化智能插座优先开关状态
      _initSmartSocketPriorityState();
      setState(() {});
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        elevation: 0,
        titleText: S.current.text('systemDetail.PowerLimit_title'),
      ),
      backgroundColor: ColorsUtil.backgroundColor,
      body: BlocConsumer<PowerLimitFetchBloc, PowerLimitFetchState>(
        listener: (context, state) {
          if (state is PowerLimitInvokeProgress) {
            CustomLoading.showLoading(null);
          } else if (state is PowerLimitInvokeSuccess) {
            CustomLoading.dismissLoading();
            Navigator.of(context).pop(state.list);
          } else if (state is PowerLimitInvokeFailure) {
            CustomToast.showToast(
                context, $t(state.msg ?? 'common.operateFail'));
            CustomLoading.dismissLoading();
          } else if (state is SmartSocketPriorityToggleProgress) {
            CustomLoading.showLoading(null);
          } else if (state is SmartSocketPriorityToggleSuccess) {
            CustomLoading.dismissLoading();
            setState(() {
              _smartSocketPriorityEnabled = state.enabled;
            });
            // 更新本地的 enableConfig
            final energyFlowChartVO = _getEnergyFlowChartVO();
            if (energyFlowChartVO != null) {
              final currentConfig = energyFlowChartVO.enableConfig ?? 0;
              final newConfig = state.enabled
                  ? currentConfig | (1 << 11) // 设置 bit11 为 1
                  : currentConfig & ~(1 << 11); // 设置 bit11 为 0
              energyFlowChartVO.enableConfig = newConfig;
            }
            CustomToast.showToast(
                context, S.current.text('common.operateSuccess'));
          } else if (state is SmartSocketPriorityToggleFailure) {
            CustomLoading.dismissLoading();
            CustomToast.showToast(
                context, $t(state.msg ?? 'common.operateFail'));
          }
        },
        buildWhen: (p, current) {
          return current is! PowerLimitFetchDialogSuccess;
        },
        builder: (context, state) {
          var children = SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: 15.h),
                _buildSmartSocketPriorityCard(),
                SizedBox(height: 20.h),
                ..._buildItems(context),
                InkResponse(
                  onTap: () {
                    if (_recordList.length == 5) {
                      CustomToast.showToast(
                          context, $t('systemDetail.Maxinum_5_time_slots'));
                      return;
                    }
                    _recordList.add(PowerLimitVM());
                    setState(() {});
                  },
                  child: _buildAddButton(),
                ),
                Text(
                  $t('systemDetail.PowerLimit_desc'),
                  style: TextStyle(color: ColorsUtil.assistTextColor),
                ),
              ],
            ),
          );

          var bottomButton = BottomButtonWidget(
            text: S.current.text('common.save'),
            onPressed: () {
              for (var element in _recordList) {
                if (element.startTime == null ||
                    element.endTime == null ||
                    element.power == null) {
                  CustomToast.showToast(
                      context, $t('systemDetail.PowerLimit_no_data'));
                  return;
                }
              }
              logger.d(
                  'results: ${PowerLimitVM.toJsonArray(_recordList).toString()}');
              List<Map<String, dynamic>> list = _recordList
                  .map((e) => {
                        'otherLoadPStart': e.startTime!.replaceAll(':', ''),
                        'otherLoadPEnd': e.endTime!.replaceAll(':', ''),
                        'otherLoadP': LocalModeManger.instance.isLocalModeNow
                            ? e.power!
                            : int.parse(e.power!)
                      })
                  .toList();
              logger.d('results: ${list.toString()}');
              if (innerInvoke) {
                BlocProvider.of<PowerLimitFetchBloc>(context).add(
                    PowerLimitInvoke(
                        list, modelKey, productKey, deviceNo, identifier));
              } else {
                Navigator.of(context).pop(list);
              }
            },
          );

          var ctStatusAlert =
              BlocSelector<MonitorFetchBloc, MonitorFetchState, String>(
            selector: (state) {
              final status =
                  state.monitorModel?.energyFlowChartVO?.ctDevice?.ctStatus;
              if (state.monitorModel?.isOffline == true ||
                  status == CTStatusType.offline) {
                return 'systemDetail.PowerLimit_ct_offline_tip';
              }
              if (status == CTStatusType.fault) {
                return 'systemDetail.PowerLimit_ct_fault_tip';
              }
              return '';
            },
            builder: (context, state) => state.isNotEmpty
                ? _buildCtOfflineTip(state)
                : const SizedBox.shrink(),
          );

          return SafeArea(
            bottom: true,
            child: Column(children: [
              if (BlocProvider.of<MonitorFetchBloc>(context)
                      .state
                      .monitorModel
                      ?.energyFlowChartVO
                      ?.ctDevice !=
                  null)
                ctStatusAlert,
              Expanded(child: children),
              bottomButton,
            ]),
          );
        },
      ),
    );
  }

  Container _buildCtOfflineTip(String msg) {
    return Container(
      height: 40.w,
      padding: const EdgeInsets.symmetric(horizontal: 20).w,
      color: ColorsUtil.themeColor.withOpacity(0.09),
      alignment: Alignment.centerLeft,
      child: Text(
        $t(msg),
        style: TextStyle(
          color: ColorsUtil.highlightTextColor,
          fontSize: 13.sp,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget _buildSmartSocketPriorityCard() {
    return CustomCard(
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  $t('systemDetail.smart_socket_priority_title'),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: ColorsUtil.textColor,
                  ),
                ),
                const Spacer(),
                CustomSwitch(
                  value: _smartSocketPriorityEnabled,
                  onToggle: (value) {
                    final enableConfig = _getEnableConfig();
                    if (enableConfig == null) {
                      CustomToast.showToast(
                          context, S.current.text('common.dataLoading'));
                      return;
                    }

                    BlocProvider.of<PowerLimitFetchBloc>(context).add(
                      SmartSocketPriorityToggled(
                        enabled: value,
                        modelKey: modelKey,
                        productKey: productKey,
                        deviceNo: deviceNo,
                        identify: identifier,
                        currentEnableConfig: enableConfig,
                      ),
                    );
                  },
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Text(
              $t('systemDetail.smart_socket_priority_on_desc'),
              style: TextStyle(
                fontSize: 12.sp,
                color: ColorsUtil.assistTextColor,
                height: 1.4,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              $t('systemDetail.smart_socket_priority_off_desc'),
              style: TextStyle(
                fontSize: 12.sp,
                color: ColorsUtil.assistTextColor,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddButton() {
    if (_recordList.length == 5) {
      return const SizedBox.shrink();
    }
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      height: 44.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.h),
        color: ColorsUtil.aboutCardColor,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CustomImageAsset(
            'icon_add_theme_color',
            width: 18.w,
            height: 18.w,
          ),
          SizedBox(width: 5.w),
          Text(
            S.current.text('systemDetail.PowerLimit_btn_text'),
            style: TextStyle(
                color: ColorsUtil.highlightTextColor,
                fontWeight: FontWeight.w400,
                fontSize: 13.sp),
          )
        ],
      ),
    );
  }

  List<Widget> _buildItems(BuildContext context) {
    List<Widget> widgets = [];
    for (int index = 0; index < _recordList.length; index++) {
      PowerLimitVM element = _recordList[index];
      Widget widget = Container(
        margin: EdgeInsets.only(bottom: 20.w),
        height: 72.w,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${S.current.text('systemDetail.Time_Slot_Item_title')} ${index + 1}',
                  style: TextStyle(
                      color: ColorsUtil.assistTextColor, fontSize: 14.sp),
                ),
                InkResponse(
                  onTap: () => {_deleteTimeSlot(index)},
                  child: CustomImageAsset(
                    'icon_delete',
                    width: 20.w,
                    height: 20.w,
                  ),
                )
              ],
            ),
            SizedBox(height: 6.w),
            SizedBox(
              height: 44.w,
              child: CustomCard(
                  child: Row(
                children: [
                  Expanded(
                      child: InkResponse(
                    onTap: () => {_switchTime(index)},
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        element.startTime != null
                            ? Text(
                                '${element.startTime}-${element.showEndTime}',
                                style: TextStyle(
                                    color: ColorsUtil.itemValueTextColor,
                                    fontSize: 13.sp))
                            : Text(
                                S.current.text('systemDetail.select_time_slot'),
                                style: TextStyle(
                                    color: ColorsUtil.assistTextColor,
                                    fontSize: 13.sp)),
                        CustomImageAsset(
                          'icon_switch_arrow',
                          width: 20.w,
                          height: 20.w,
                        )
                      ],
                    ),
                  )),
                  Container(
                    height: 15.w,
                    width: 1,
                    color: ColorsUtil.dividerColor,
                  ),
                  Expanded(
                      child: InkResponse(
                    onTap: () => {_switchPower(index)},
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        element.power != null
                            ? Text('${element.power}W',
                                style: TextStyle(
                                    color: ColorsUtil.itemValueTextColor,
                                    fontSize: 13.sp))
                            : Text(S.current.text('systemDetail.select_power'),
                                style: TextStyle(
                                    color: ColorsUtil.assistTextColor,
                                    fontSize: 13.sp)),
                        CustomImageAsset(
                          'icon_switch_arrow',
                          width: 20.w,
                          height: 20.w,
                        )
                      ],
                    ),
                  )),
                ],
              )),
            )
          ],
        ),
      );
      widgets.add(widget);
    }
    return widgets;
  }

  _deleteTimeSlot(int index) {
    logger.d('click index $index');
    _recordList.removeAt(index);
    setState(() {});
  }

  _switchTime(int recordIndex) {
    PowerLimitVM? time = _recordList[recordIndex];
    TimeSlotRangePicker.showStringPicker(context,
        startTime: time.startTime,
        minuteInterval: 15,
        endTime: time.showEndTime, clickCallBack: (leftValue, rightValue) {
      List<String> startTimes = [];
      List<String> endTimes = [];
      List<PowerLimitVM> tempList = List.from(_recordList);
      tempList.removeAt(recordIndex);
      for (var element in tempList) {
        if (element.startTime != null && element.startTime!.isNotEmpty) {
          startTimes.add(element.startTime!);
          endTimes.add(element.endTime!);
        }
      }
      final softVar = context
              .read<MqttBloc>()
              .deviceList
              .firstWhere((element) => element.productKey == 'HB-EMS')
              .softVer ??
          "";
      final factoryStr = context
              .read<MonitorFetchBloc>()
              .state
              .monitorModel
              ?.systemVO
              ?.factoryModel ??
          "";
      if (isOtherLoadSupport2400(factoryStr, softVar)) {
      } else {
        if (rightValue == '00:00') rightValue = '23:59';
      }
      logger.d('_switchTime startTimes $startTimes , endTimes $endTimes ');
      if (TimeUtils.checkTimes(leftValue, rightValue, startTimes, endTimes)) {
        CustomToast.showToast(context, $t('component.Time_has_intersection'));
        return;
      }
      // 当支持 24:00, 不支持结束时间选择00:00
      if (isOtherLoadSupport2400(factoryStr, softVar) &&
          rightValue == '00:00') {
        CustomToast.showToast(
            context, $t('component.Start_time_must_be_less_than_end_time'));
        return;
      }
      if (!TimeUtils.checkStartTimeLessThanEndTime(leftValue, rightValue)) {
        CustomToast.showToast(context,
            S.current.text('component.Start_time_must_be_less_than_end_time'));
        return;
      }
      logger.d('_switchTime leftValue $leftValue , rightValue $rightValue ');
      _recordList[recordIndex].startTime = leftValue;
      _recordList[recordIndex].endTime = rightValue;
      setState(() {});
    },
        height: 220.h,
        data: ['100w', '200w', '300w', '400w', '500w', '600w', '700w', '800w'],
        title: S.current.text('component.Choose_Time_Slot'));
  }

  _switchPower(int recordIndex) {
    var dataList = SystemConfig.powerSelectData;
    var selected = dataList.firstWhere(
        (element) => element.toString() == _recordList[recordIndex].power,
        orElse: () => -999999);
    var selectedIndex = dataList.indexOf(selected);
    logger.d('_switchPower selected $selected selectedIndex $selectedIndex');
    SinglePicker.showStringPicker(
      context,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      secondButtonType: SecondButtonType.input,
      textInputType:
          const TextInputType.numberWithOptions(signed: true, decimal: false),
      inputValidator: (value) {
        final v = int.parse(value);
        if (v < 50) {
          CustomToast.showToast(
              context, $t('systemDetail.Power_limit_custom_value_validate'));
          return false;
        }
        if (v > dataList.last) {
          CustomToast.showToast(
              context, $t('systemDetail.other_load_custom_value_validate_max'));
          return false;
        }

        return true;
      },
      clickCallBack: (value, index) {
        dynamic powerLimit;
        if (index == -1) {
          powerLimit = value;
        } else {
          powerLimit = dataList[index];
        }
        logger.d('_switchPower : value : $powerLimit , index : $index');
        _recordList[recordIndex].power = powerLimit.toString();
        setState(() {});
      },
      selectIndex: selectedIndex,
      height: 220.h,
      inputText: _recordList[recordIndex].power,
      data: SystemConfig.powerSelectData.map((e) => '${e}W').toList(),
      originData: SystemConfig.powerSelectData,
      title: S.current.text('systemDetail.Power_limit'),
    );
  }

  /// 获取能流图VO对象
  EnergyFlowChartVo? _getEnergyFlowChartVO() {
    return BlocProvider.of<MonitorFetchBloc>(context)
        .state
        .monitorModel
        ?.energyFlowChartVO;
  }

  /// 获取使能配置值
  int? _getEnableConfig() {
    return _getEnergyFlowChartVO()?.enableConfig;
  }

  /// 检查指定位是否启用
  /// 返回 null 表示数据未加载完成
  bool? _isBitEnabled(int bitPosition) {
    final enableConfig = _getEnableConfig();
    if (enableConfig == null) return null; // 数据未加载完成
    return (enableConfig & (1 << bitPosition)) != 0;
  }

  /// 初始化智能插座优先开关状态
  void _initSmartSocketPriorityState() {
    // bit11 控制智能插座优先功能，1使能，0不使能
    final enabled = _isBitEnabled(11);
    if (enabled != null) {
      _smartSocketPriorityEnabled = enabled;
    }
  }
}
