import 'package:equatable/equatable.dart';

abstract class PowerLimitFetchEvent {
  const PowerLimitFetchEvent();
}

class PowerLimitFetched extends PowerLimitFetchEvent {}

class PowerLimitDialogFetched extends PowerLimitFetchEvent {}

class PowerLimitInvoke extends PowerLimitFetchEvent {
  dynamic value;
  String modelKey;
  String productKey;
  String deviceNo;
  String identify;

  PowerLimitInvoke(
      this.value, this.modelKey, this.productKey, this.deviceNo, this.identify);
}

class SmartSocketPriorityToggled extends PowerLimitFetchEvent {
  final bool enabled;
  final String modelKey;
  final String productKey;
  final String deviceNo;
  final String identify;

  SmartSocketPriorityToggled({
    required this.enabled,
    required this.modelKey,
    required this.productKey,
    required this.deviceNo,
    required this.identify,
  });
}
