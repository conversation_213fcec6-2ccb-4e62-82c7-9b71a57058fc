import 'package:equatable/equatable.dart';

abstract class PowerLimitFetchEvent {
  const PowerLimitFetchEvent();
}

class PowerLimitFetched extends PowerLimitFetchEvent {}

class PowerLimitDialogFetched extends PowerLimitFetchEvent {}

class PowerLimitInvoke extends PowerLimitFetchEvent {
  dynamic value;
  String modelKey;
  String productKey;
  String deviceNo;
  String identify;

  PowerLimitInvoke(
      this.value, this.modelKey, this.productKey, this.deviceNo, this.identify);
}
