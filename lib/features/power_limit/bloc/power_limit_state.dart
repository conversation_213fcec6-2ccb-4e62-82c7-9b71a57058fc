abstract class PowerLimitFetchState {
  const PowerLimitFetchState();
}

class PowerLimitFetchInitial extends PowerLimitFetchState {}

class PowerLimitFetchInProgress extends PowerLimitFetchState {}

class PowerLimitFetchSuccess extends PowerLimitFetchState {}

class PowerLimitFetchFailure extends PowerLimitFetchState {}

class PowerLimitFetchDialogSuccess extends PowerLimitFetchState {}

class PowerLimitInvokeProgress extends PowerLimitFetchState {}

class PowerLimitInvokeSuccess extends PowerLimitFetchState {
  List<Map<String, dynamic>> list;

  PowerLimitInvokeSuccess(this.list);
}

class PowerLimitInvokeFailure extends PowerLimitFetchState {
  final String? msg;

  PowerLimitInvokeFailure(this.msg);
}

class SmartSocketPriorityToggleProgress extends PowerLimitFetchState {}

class SmartSocketPriorityToggleSuc<PERSON> extends PowerLimitFetchState {
  final bool enabled;

  SmartSocketPriorityToggleSuccess(this.enabled);
}

class SmartSocketPriorityToggleFailure extends PowerLimitFetchState {
  final String? msg;

  SmartSocketPriorityToggleFailure(this.msg);
}
