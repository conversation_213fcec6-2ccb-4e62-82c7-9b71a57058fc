import 'package:flutter_basic/platform/bluetooth/local_mode_manger.dart';
import 'package:flutter_basic/platform/platform.dart';
import 'package:flutter_basic/repositories/custom_exception/ServiceException.dart';
import 'package:flutter_basic/repositories/system_repository/system_client_repository.dart';

import '../../../platform/community/monitor/constants.dart';
import '../../../platform/community/monitor/data_parsers.dart';
import 'bloc.dart';

class PowerLimitFetchBloc
    extends Bloc<PowerLimitFetchEvent, PowerLimitFetchState> {
  PowerLimitFetchBloc() : super(PowerLimitFetchInitial());

  @override
  Stream<PowerLimitFetchState> mapEventToState(
      PowerLimitFetchEvent event) async* {
    if (event is PowerLimitInvoke) {
      yield* _mapOtherLoadInvokeStartToState(event);
    } else if (event is SmartSocketPriorityToggled) {
      yield* _mapSmartSocketPriorityToggleToState(event);
    }
  }

  Stream<PowerLimitFetchState> _mapOtherLoadInvokeStartToState(
      PowerLimitInvoke event) async* {
    yield PowerLimitInvokeProgress();
    try {
      if (LocalModeManger.instance.isLocalModeNow) {
        var value = event.value as List<Map<String, dynamic>>;
        var list = formatOtherLoadControlData(value);
        await LocalModeManger.instance
            .setDeviceData(sn: event.deviceNo, params: list);
      } else {
        await SystemClientRepository.instance.invoke(event.deviceNo,
            event.modelKey, event.identify, event.productKey, event.value);
      }
      yield PowerLimitInvokeSuccess(event.value);
    } catch (_, s) {
      logger.d('error ${_.toString()} ${s.toString()}');

      final msg = _ is ServiceException ? _.msg : null;
      yield PowerLimitInvokeFailure(msg);
    }
  }

  Stream<PowerLimitFetchState> _mapSmartSocketPriorityToggleToState(
      SmartSocketPriorityToggled event) async* {
    yield SmartSocketPriorityToggleProgress();
    try {
      // 计算新的使能配置值 (bit11 控制智能插座优先)
      final newEnableConfig =
          _setBitValue(event.currentEnableConfig, 11, event.enabled);

      if (LocalModeManger.instance.isLocalModeNow) {
        // 本地模式下，构建参数列表格式，需要使用 ems_ 前缀
        final deviceSn = event.deviceNo.startsWith('ems_')
            ? event.deviceNo
            : 'ems_${event.deviceNo}';
        List<dynamic> params = [
          [diy_point_enable_config, newEnableConfig.toString()],
        ];
        await LocalModeManger.instance
            .setDeviceData(sn: deviceSn, params: params);
      } else {
        // 云端模式下，构建参数Map格式
        Map<String, dynamic> params = {
          diy_point_enable_config: newEnableConfig,
        };
        await SystemClientRepository.instance.invoke(event.deviceNo,
            event.modelKey, event.identify, event.productKey, params);
      }
      yield SmartSocketPriorityToggleSuccess(event.enabled);
    } catch (_, s) {
      logger.d(
          'SmartSocketPriority toggle error ${_.toString()} ${s.toString()}');

      final msg = _ is ServiceException ? _.msg : null;
      yield SmartSocketPriorityToggleFailure(msg);
    }
  }

  /// 设置指定位的值
  int _setBitValue(int enableConfig, int bitPosition, bool enabled) {
    return enabled
        ? enableConfig | (1 << bitPosition) // 设置位为1
        : enableConfig & ~(1 << bitPosition); // 设置位为0
  }
}
